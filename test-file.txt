CYBER BANGLA LAB - TEST FILE
============================

This is a test file created for demonstration purposes.

If you can read this file through the URL Status Checker application,
it means the Local File Inclusion (LFI) vulnerability is working.

File Information:
- Filename: test-file.txt
- Purpose: <PERSON><PERSON> demonstration
- Created by: TareqAhamed (0xt4req)

Security Note:
This demonstrates how an attacker could potentially read sensitive files
from the server filesystem using the file:// protocol.

In a real-world scenario, attackers might try to access:
- /etc/passwd (Linux user accounts)
- /etc/shadow (Linux password hashes)
- /var/log/apache2/access.log (Web server logs)
- /proc/version (System information)
- C:\Windows\System32\drivers\etc\hosts (Windows hosts file)
- Configuration files containing database credentials
- Application source code files

Always implement proper input validation and protocol restrictions!

---
Cyber Bangla Lab - Educational Security Training
