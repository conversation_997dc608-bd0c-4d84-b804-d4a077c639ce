FROM php:8.2-apache

# Install required packages
RUN apt-get update && apt-get install -y \
    curl \
    iputils-ping \
    net-tools \
    dnsutils \
    procps \
    && docker-php-ext-install mysqli

# Enable Apache mod_rewrite (optional, for future upgrades)
RUN a2enmod rewrite

# Copy lab files to web root
COPY . /var/www/html/

# Set permissions
RUN chown -R www-data:www-data /var/www/html

EXPOSE 80
