# Cyber Bangla Lab - cURL-based LFI Vulnerability

## Overview
This is a vulnerable PHP web application designed for educational purposes to demonstrate Local File Inclusion (LFI) and Server-Side Request Forgery (SSRF) vulnerabilities through misuse of cURL.

**⚠️ WARNING: This application is intentionally vulnerable and should only be used in controlled environments for educational purposes.**

## Vulnerability Description

### The Application
- **Purpose**: URL Status Checker that allows users to input URLs to check if websites are online
- **Technology**: Plain PHP with cURL
- **Validation**: Uses `preg_match()` and `escapeshellcmd()` for input validation (ineffective)

### The Vulnerability
Despite implementing input validation, the application is vulnerable to:

1. **Local File Inclusion (LFI)** via `file://` protocol
2. **Server-Side Request Forgery (SSRF)** via various protocols
3. **Information Disclosure** through response content display

### Vulnerable Code Analysis

```php
// Flawed validation - allows various protocols
if (preg_match('/^[a-zA-Z0-9\-\._~:\/?#\[\]@!$&\'()*+,;=%]+$/', $url)) {
    
    // Ineffective sanitization against file:// schemes
    $sanitized_url = escapeshellcmd($url);
    
    // Vulnerable cURL configuration
    curl_setopt($ch, CURLOPT_URL, $sanitized_url);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    // ... other options
}
```

## Exploitation Examples

### 1. Local File Inclusion (Linux)
```
file:///etc/passwd
file:///etc/hosts
file:///proc/version
file:///var/log/apache2/access.log
```

### 2. Local File Inclusion (Windows)
```
file:///C:/Windows/System32/drivers/etc/hosts
file:///C:/Windows/win.ini
file:///C:/xampp/apache/logs/access.log
```

### 3. SSRF Attacks
```
http://127.0.0.1:80
http://localhost:3306
http://***************/latest/meta-data/
gopher://127.0.0.1:25/
dict://127.0.0.1:11211/
```

### 4. Protocol Exploitation
```
ftp://127.0.0.1/
ldap://127.0.0.1/
tftp://127.0.0.1/
```

## Why the Validation Fails

### 1. Regex Pattern Issues
- The regex `^[a-zA-Z0-9\-\._~:\/?#\[\]@!$&\'()*+,;=%]+$` is too permissive
- Allows `file://`, `gopher://`, `dict://`, and other dangerous protocols
- Doesn't restrict to HTTP/HTTPS only

### 2. escapeshellcmd() Limitations
- `escapeshellcmd()` is designed for shell command sanitization
- Does NOT prevent URL scheme exploitation
- Doesn't block `file://` or other dangerous protocols

### 3. cURL Configuration
- `CURLOPT_FOLLOWLOCATION` allows redirects (can be chained)
- No protocol restrictions set
- SSL verification disabled

## Proper Mitigation

### 1. Protocol Whitelist
```php
$allowed_schemes = ['http', 'https'];
$parsed_url = parse_url($url);
if (!in_array($parsed_url['scheme'], $allowed_schemes)) {
    die('Only HTTP and HTTPS protocols are allowed');
}
```

### 2. URL Validation
```php
if (!filter_var($url, FILTER_VALIDATE_URL) || 
    !preg_match('/^https?:\/\//', $url)) {
    die('Invalid URL format');
}
```

### 3. Secure cURL Configuration
```php
curl_setopt($ch, CURLOPT_PROTOCOLS, CURLPROTO_HTTP | CURLPROTO_HTTPS);
curl_setopt($ch, CURLOPT_REDIR_PROTOCOLS, CURLPROTO_HTTP | CURLPROTO_HTTPS);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
```

### 4. Network Restrictions
```php
// Block private/internal IP ranges
$ip = gethostbyname(parse_url($url, PHP_URL_HOST));
if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
    die('Access to private/internal networks is not allowed');
}
```

## Setup Instructions

1. Place files in your web server directory (e.g., `/var/www/html/` or `C:\xampp\htdocs\`)
2. Ensure PHP and cURL extension are installed
3. Access via web browser: `http://localhost/curl-based-lfi/`

## Educational Objectives

Students will learn:
- How input validation can be bypassed
- The dangers of unrestricted cURL usage
- Various attack vectors (LFI, SSRF)
- Proper mitigation techniques
- The importance of protocol restrictions

## Legal Disclaimer

This application is created solely for educational and training purposes. Users are responsible for ensuring they comply with all applicable laws and regulations. The developer assumes no responsibility for any misuse of this application.

---

**Developed by TareqAhamed (0xt4req)**  
*Cyber Security Education & Training*
