# Cyber Bangla Lab - cURL-based LFI Vulnerability

## Overview
This is a vulnerable PHP web application designed for educational purposes to demonstrate Local File Inclusion (LFI) and Server-Side Request Forgery (SSRF) vulnerabilities through misuse of cURL.

**⚠️ WARNING: This application is intentionally vulnerable and should only be used in controlled environments for educational purposes.**

## Vulnerability Description

### The Application
- **Purpose**: URL Status Checker that allows users to input URLs to check if websites are online
- **Technology**: Plain PHP with cURL
- **Validation**: Uses `preg_match()`, `escapeshellcmd()`, and basic SSRF protection (all ineffective)

### The Vulnerability
Despite implementing input validation, the application is vulnerable to:

1. **Local File Inclusion (LFI)** via `file://` protocol
2. **Server-Side Request Forgery (SSRF)** via various protocols
3. **Information Disclosure** through response content display

### Vulnerable Code Analysis

```php
// Flawed validation - only checks URL prefix
if (preg_match('/^https?:\/\//', $url)) {

    // Ineffective sanitization against file:// schemes
    $sanitized_url = escapeshellcmd($url);

    // Basic SSRF protection (easily bypassed)
    $parsed_url = parse_url($sanitized_url);
    $host = $parsed_url['host'] ?? '';

    if (preg_match('/^(localhost|127\.0\.0\.1|0\.0\.0\.0|::1)$/i', $host)) {
        // Block localhost
    } else if (preg_match('/^(10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.)/', $host)) {
        // Block private IPs
    } else {
        // Vulnerable cURL configuration
        curl_setopt($ch, CURLOPT_URL, $sanitized_url);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        // ... other options
    }
}
```

## Exploitation Examples

### 1. Local File Inclusion (Linux)
```
file:///etc/passwd
file:///etc/hosts
file:///proc/version
file:///var/log/apache2/access.log
```

### 2. Local File Inclusion (Windows)
```
file:///C:/Windows/System32/drivers/etc/hosts
file:///C:/Windows/win.ini
file:///C:/xampp/apache/logs/access.log
```

### 3. SSRF Bypass Techniques
```
http://0x7f000001/          (Hex encoding of 127.0.0.1)
http://2130706433/          (Decimal encoding of 127.0.0.1)
http://017700000001/        (Octal encoding of 127.0.0.1)
http://[::1]/               (IPv6 localhost)
http://127.1/               (Short form of 127.0.0.1)
http://evil.com@127.0.0.1/  (URL with credentials)
http://127.0.0.1.evil.com/  (Subdomain bypass)
```

### 4. Redirect-based Attacks
```
http://evil.com/redirect-to-localhost
http://bit.ly/short-to-internal
http://redirector.com/?url=file:///etc/passwd
```

## Why the Validation Fails

### 1. Incomplete Protocol Validation
- Only checks if URL starts with `http://` or `https://`
- Doesn't validate the entire URL structure
- Can be bypassed with redirects to dangerous protocols

### 2. Weak SSRF Protection
- Only blocks obvious localhost/private IP patterns
- Doesn't handle IP encoding variations (hex, decimal, octal)
- Doesn't block IPv6 localhost (`::1`)
- Doesn't prevent DNS rebinding attacks
- Doesn't check for URL redirects

### 3. escapeshellcmd() Limitations
- `escapeshellcmd()` is designed for shell command sanitization
- Does NOT prevent URL scheme exploitation after redirects
- Ineffective against HTTP-based attacks

### 4. cURL Configuration
- `CURLOPT_FOLLOWLOCATION` allows redirects (can be chained)
- No protocol restrictions set in cURL options
- SSL verification disabled
- No timeout restrictions on redirects

## Proper Mitigation

### 1. Protocol Whitelist
```php
$allowed_schemes = ['http', 'https'];
$parsed_url = parse_url($url);
if (!in_array($parsed_url['scheme'], $allowed_schemes)) {
    die('Only HTTP and HTTPS protocols are allowed');
}
```

### 2. URL Validation
```php
if (!filter_var($url, FILTER_VALIDATE_URL) || 
    !preg_match('/^https?:\/\//', $url)) {
    die('Invalid URL format');
}
```

### 3. Secure cURL Configuration
```php
curl_setopt($ch, CURLOPT_PROTOCOLS, CURLPROTO_HTTP | CURLPROTO_HTTPS);
curl_setopt($ch, CURLOPT_REDIR_PROTOCOLS, CURLPROTO_HTTP | CURLPROTO_HTTPS);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
```

### 4. Network Restrictions
```php
// Block private/internal IP ranges
$ip = gethostbyname(parse_url($url, PHP_URL_HOST));
if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
    die('Access to private/internal networks is not allowed');
}
```

## Setup Instructions

1. Place files in your web server directory (e.g., `/var/www/html/` or `C:\xampp\htdocs\`)
2. Ensure PHP and cURL extension are installed
3. Access via web browser: `http://localhost/curl-based-lfi/`

## Educational Objectives

Students will learn:
- How input validation can be bypassed
- The dangers of unrestricted cURL usage
- Various attack vectors (LFI, SSRF)
- Proper mitigation techniques
- The importance of protocol restrictions

## Legal Disclaimer

This application is created solely for educational and training purposes. Users are responsible for ensuring they comply with all applicable laws and regulations. The developer assumes no responsibility for any misuse of this application.

---

**Developed by TareqAhamed (0xt4req)**  
*Cyber Security Education & Training*
