# Cyber Bangla Lab - Apache Configuration
# This file ensures proper handling of the vulnerable application

# Enable PHP processing
<Files "*.php">
    SetHandler application/x-httpd-php
</Files>

# Set default index file
DirectoryIndex index.php

# Security headers (ironic for a vulnerable app, but good practice)
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Enable error reporting for educational purposes
php_flag display_errors On
php_flag display_startup_errors On
php_value error_reporting "E_ALL"

# Increase execution time for cURL operations
php_value max_execution_time 30

# Allow larger POST data for long URLs
php_value post_max_size 8M
php_value max_input_vars 1000

# Custom error pages
ErrorDocument 404 /404.html
ErrorDocument 500 /500.html

# Prevent access to sensitive files (except for educational purposes)
<Files "README.md">
    Order allow,deny
    Allow from all
</Files>

<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

# MIME types
AddType text/plain .txt
AddType text/markdown .md

# Compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript
</IfModule>

# Cache control for static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
</IfModule>
