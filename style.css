/* Cyber Security Dark Theme with Teal Accents */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0c1a2b 0%, #1e3b5d 100%);
    color: #e0e6ed;
    min-height: 100vh;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px 0;
    border-bottom: 2px solid #00c9a7;
    background: rgba(0, 201, 167, 0.05);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

header h1 {
    font-size: 3rem;
    color: #00c9a7;
    text-shadow: 0 0 20px rgba(0, 201, 167, 0.5);
    margin-bottom: 10px;
    font-weight: 700;
}

header h2 {
    font-size: 1.8rem;
    color: #4fd1c7;
    margin-bottom: 10px;
}

.subtitle {
    color: #a0aec0;
    font-size: 1.1rem;
    font-style: italic;
}

/* Main Content */
main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Form Styles */
.form-container {
    background: rgba(30, 59, 93, 0.8);
    padding: 40px;
    border-radius: 15px;
    border: 1px solid #00c9a7;
    box-shadow: 0 10px 30px rgba(0, 201, 167, 0.1);
    backdrop-filter: blur(10px);
}

.input-group {
    margin-bottom: 25px;
}

label {
    display: block;
    margin-bottom: 8px;
    color: #00c9a7;
    font-weight: 600;
    font-size: 1.1rem;
}

input[type="text"] {
    width: 100%;
    padding: 15px;
    border: 2px solid #2d5a87;
    border-radius: 8px;
    background: rgba(12, 26, 43, 0.9);
    color: #e0e6ed;
    font-size: 1rem;
    transition: all 0.3s ease;
}

input[type="text"]:focus {
    outline: none;
    border-color: #00c9a7;
    box-shadow: 0 0 15px rgba(0, 201, 167, 0.3);
    background: rgba(12, 26, 43, 1);
}

input[type="text"]::placeholder {
    color: #718096;
}

.check-btn {
    background: linear-gradient(135deg, #00c9a7 0%, #4fd1c7 100%);
    color: #0c1a2b;
    border: none;
    padding: 15px 40px;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.check-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 201, 167, 0.4);
}

.check-btn:active {
    transform: translateY(0);
}

.check-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Result Styles */
.result-container {
    background: rgba(30, 59, 93, 0.9);
    padding: 30px;
    border-radius: 15px;
    border: 1px solid #4fd1c7;
    box-shadow: 0 10px 30px rgba(79, 209, 199, 0.1);
}

.result-container h3 {
    color: #00c9a7;
    margin-bottom: 20px;
    font-size: 1.4rem;
    border-bottom: 1px solid #4fd1c7;
    padding-bottom: 10px;
}

.success {
    background: rgba(0, 201, 167, 0.1);
    border: 1px solid #00c9a7;
    color: #00c9a7;
    padding: 12px 20px;
    border-radius: 8px;
    margin: 10px 0;
    font-weight: 600;
}

.error {
    background: rgba(255, 99, 99, 0.1);
    border: 1px solid #ff6363;
    color: #ff6363;
    padding: 12px 20px;
    border-radius: 8px;
    margin: 10px 0;
    font-weight: 600;
}

.info {
    background: rgba(79, 209, 199, 0.1);
    border: 1px solid #4fd1c7;
    color: #4fd1c7;
    padding: 10px 20px;
    border-radius: 8px;
    margin: 8px 0;
}

.response-content {
    margin-top: 20px;
    background: rgba(12, 26, 43, 0.8);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #2d5a87;
}

.response-content h4 {
    color: #00c9a7;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.response-content pre {
    background: rgba(0, 0, 0, 0.5);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #4fd1c7;
    color: #e0e6ed;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.truncated {
    color: #a0aec0;
    font-style: italic;
    margin-top: 10px;
}

/* Footer Styles */
footer {
    margin-top: 40px;
    padding: 30px 0;
    border-top: 2px solid #00c9a7;
    background: rgba(0, 201, 167, 0.05);
    border-radius: 10px;
    text-align: center;
}

.footer-content p {
    margin: 5px 0;
}

.highlight {
    color: #00c9a7;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(0, 201, 167, 0.3);
}

.warning {
    color: #ff6363;
    font-size: 0.9rem;
    font-weight: 500;
    margin-top: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2.2rem;
    }
    
    header h2 {
        font-size: 1.4rem;
    }
    
    .form-container {
        padding: 25px;
    }
    
    .result-container {
        padding: 20px;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1e3b5d;
}

::-webkit-scrollbar-thumb {
    background: #00c9a7;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #4fd1c7;
}
