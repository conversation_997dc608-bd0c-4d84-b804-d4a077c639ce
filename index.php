<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Status Checker - Cyber Bangla Lab</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Cyber Bangla Lab</h1>
            <h2>URL Status Checker</h2>
            <p class="subtitle">Check if a website is online and get its HTTP status</p>
        </header>

        <main>
            <div class="form-container">
                <form method="POST" action="">
                    <div class="input-group">
                        <label for="url">Enter URL to check:</label>
                        <input type="text" id="url" name="url" placeholder="https://google.com" 
                               value="<?php echo isset($_POST['url']) ? htmlspecialchars($_POST['url']) : ''; ?>" required>
                    </div>
                    <button type="submit" class="check-btn">Check Status</button>
                </form>
            </div>

            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['url'])) {
                $url = $_POST['url'];
                
                echo '<div class="result-container">';
                echo '<h3>Results for: ' . htmlspecialchars($url) . '</h3>';
                
                // Validation Logic (intentionally flawed)
                // Check if input looks like a domain or URL
                if (preg_match('/^[a-zA-Z0-9\-\._~:\/?#\[\]@!$&\'()*+,;=%]+$/', $url)) {
                    
                    // Apply "sanitization" (ineffective against file:// schemes)
                    $sanitized_url = escapeshellcmd($url);
                    
                    echo '<div class="info">✓ URL validation passed</div>';
                    echo '<div class="info">✓ Input sanitized with escapeshellcmd()</div>';
                    
                    // Initialize cURL
                    $ch = curl_init();
                    
                    // Set cURL options (vulnerable configuration)
                    curl_setopt($ch, CURLOPT_URL, $sanitized_url);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                    curl_setopt($ch, CURLOPT_USERAGENT, 'CyberBanglaLab-StatusChecker/1.0');
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                    
                    // Execute the request
                    $response = curl_exec($ch);
                    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
                    $total_time = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
                    $error = curl_error($ch);
                    
                    curl_close($ch);
                    
                    if ($error) {
                        echo '<div class="error">cURL Error: ' . htmlspecialchars($error) . '</div>';
                    } else {
                        echo '<div class="success">HTTP Status Code: ' . $http_code . '</div>';
                        
                        if ($content_type) {
                            echo '<div class="info">Content Type: ' . htmlspecialchars($content_type) . '</div>';
                        }
                        
                        echo '<div class="info">Response Time: ' . round($total_time, 2) . ' seconds</div>';
                        
                        // Display response content (dangerous for file:// URLs)
                        if ($response) {
                            echo '<div class="response-content">';
                            echo '<h4>Response Content Preview:</h4>';
                            echo '<pre>' . htmlspecialchars(substr($response, 0, 2000)) . '</pre>';
                            if (strlen($response) > 2000) {
                                echo '<p class="truncated">... (content truncated)</p>';
                            }
                            echo '</div>';
                        }
                    }
                    
                } else {
                    echo '<div class="error">❌ Invalid URL format. Please enter a valid URL.</div>';
                }
                
                echo '</div>';
            }
            ?>
        </main>

        <footer>
            <div class="footer-content">
                <p>Developed by <span class="highlight">TareqAhamed (0xt4req)</span></p>
                <p class="warning">⚠️ This is a vulnerable application for educational purposes only</p>
            </div>
        </footer>
    </div>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const input = document.querySelector('#url');
            
            form.addEventListener('submit', function() {
                const button = document.querySelector('.check-btn');
                button.textContent = 'Checking...';
                button.disabled = true;
            });
            
            // Example URLs for demonstration
            const examples = [
                'https://google.com',
                'https://github.com',
                'file:///etc/passwd',
                'file:///windows/system32/drivers/etc/hosts',
                'gopher://127.0.0.1:70/',
                'dict://127.0.0.1:2628/'
            ];
            
            input.addEventListener('focus', function() {
                if (!this.value) {
                    this.placeholder = examples[Math.floor(Math.random() * examples.length)];
                }
            });
        });
    </script>
</body>
</html>
