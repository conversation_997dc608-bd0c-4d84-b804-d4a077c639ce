<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Status Checker - Cyber Bangla Lab</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Cyber Bangla Lab</h1>
            <h2>URL Status Checker</h2>
            <p class="subtitle">Check if a website is online and get its HTTP status</p>
        </header>

        <main>
            <div class="form-container">
                <form method="POST" action="">
                    <div class="input-group">
                        <label for="url">Enter URL to check:</label>
                        <input type="text" id="url" name="url" placeholder="https://google.com" 
                               value="<?php echo isset($_POST['url']) ? htmlspecialchars($_POST['url']) : ''; ?>" required>
                    </div>
                    <button type="submit" class="check-btn">Check Status</button>
                </form>
            </div>

            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['url'])) {
                $url = $_POST['url'];
                
                echo '<div class="result-container">';
                echo '<h3>Results for: ' . htmlspecialchars($url) . '</h3>';
                
                // Validation Logic (intentionally flawed)
                // Check if URL starts with http:// or https://
                if (preg_match('/^https?:\/\//', $url)) {

                    // Apply "sanitization" (ineffective against file:// schemes)
                    $sanitized_url = escapeshellcmd($url);

                    // Comprehensive SSRF Protection
                    $parsed_url = parse_url($sanitized_url);
                    $host = $parsed_url['host'] ?? '';

                    // Function to check if IP is private/internal
                    function isPrivateIP($ip) {
                        // Convert to long integer for easier comparison
                        $ip_long = ip2long($ip);
                        if ($ip_long === false) return true; // Invalid IP, block it

                        // Check private ranges
                        $private_ranges = [
                            ['10.0.0.0', '1*************'],
                            ['**********', '**************'],
                            ['***********', '***************'],
                            ['*********', '***************'],
                            ['***********', '***************'], // Link-local
                            ['0.0.0.0', '*************'],       // Current network
                        ];

                        foreach ($private_ranges as $range) {
                            $start = ip2long($range[0]);
                            $end = ip2long($range[1]);
                            if ($ip_long >= $start && $ip_long <= $end) {
                                return true;
                            }
                        }
                        return false;
                    }

                    // Resolve hostname to IP and check
                    $resolved_ips = [];
                    if (filter_var($host, FILTER_VALIDATE_IP)) {
                        // Direct IP provided
                        $resolved_ips[] = $host;
                    } else {
                        // Resolve hostname
                        $resolved_ips = gethostbynamel($host);
                        if ($resolved_ips === false) {
                            echo '<div class="error">❌ Unable to resolve hostname</div>';
                            $resolved_ips = [];
                        }
                    }

                    // Check all resolved IPs
                    $blocked = false;
                    foreach ($resolved_ips as $ip) {
                        if (isPrivateIP($ip)) {
                            echo '<div class="error">❌ Access to private/internal IP addresses is blocked: ' . htmlspecialchars($ip) . '</div>';
                            $blocked = true;
                            break;
                        }

                        // Additional IPv6 checks
                        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
                            if (in_array($ip, ['::1', '::ffff:127.0.0.1']) ||
                                strpos($ip, 'fe80:') === 0 ||
                                strpos($ip, 'fc00:') === 0 ||
                                strpos($ip, 'fd00:') === 0) {
                                echo '<div class="error">❌ Access to private IPv6 addresses is blocked: ' . htmlspecialchars($ip) . '</div>';
                                $blocked = true;
                                break;
                            }
                        }
                    }

                    if (!$blocked && !empty($resolved_ips)) {

                        // Use command-line curl with -s (silent) flag
                        $curl_command = "curl -s -w '%{http_code}|%{content_type}|%{time_total}' -m 10 " . escapeshellarg($sanitized_url);

                        // Execute curl command
                        $start_time = microtime(true);
                        $output = shell_exec($curl_command . ' 2>&1');
                        $execution_time = microtime(true) - $start_time;

                        if ($output === null) {
                            echo '<div class="error">❌ Failed to execute curl command</div>';
                        } else {
                            // Parse the output - curl -w format: content|http_code|content_type|time_total
                            $parts = explode('|', $output);

                            if (count($parts) >= 3) {
                                $response_content = substr($output, 0, strrpos($output, '|', strrpos($output, '|', strrpos($output, '|') - 1) - 1));
                                $time_total = array_pop($parts);
                                $content_type = array_pop($parts);
                                $http_code = array_pop($parts);

                                // Display results
                                if ($http_code && is_numeric($http_code)) {
                                    echo '<div class="success">HTTP Status Code: ' . htmlspecialchars($http_code) . '</div>';
                                } else {
                                    echo '<div class="error">Unable to determine HTTP status</div>';
                                }

                                if ($content_type && $content_type !== '-') {
                                    echo '<div class="info">Content Type: ' . htmlspecialchars($content_type) . '</div>';
                                }

                                if ($time_total && is_numeric($time_total)) {
                                    echo '<div class="info">Response Time: ' . round(floatval($time_total), 2) . ' seconds</div>';
                                }

                                echo '<div class="info">Command Execution Time: ' . round($execution_time, 2) . ' seconds</div>';

                                // Display response content (this is where the vulnerability lies)
                                if ($response_content && strlen(trim($response_content)) > 0) {
                                    echo '<div class="response-content">';
                                    echo '<h4>Response Content Preview:</h4>';
                                    echo '<pre>' . htmlspecialchars(substr($response_content, 0, 2000)) . '</pre>';
                                    if (strlen($response_content) > 2000) {
                                        echo '<p class="truncated">... (content truncated)</p>';
                                    }
                                    echo '</div>';
                                }
                            } else {
                                echo '<div class="error">Unexpected curl output format</div>';
                                echo '<div class="info">Raw output: ' . htmlspecialchars($output) . '</div>';
                            }
                        }
                    }
                } else {
                    echo '<div class="error">❌ Invalid URL format. Please enter a valid URL.</div>';
                }
                
                echo '</div>';
            }
            ?>
        </main>

        <footer>
            <div class="footer-content">
                <p>Developed by <span class="highlight">TareqAhamed (0xt4req)</span></p>
                <p class="warning">⚠️ This is a vulnerable application for educational purposes only</p>
            </div>
        </footer>
    </div>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const input = document.querySelector('#url');
            
            form.addEventListener('submit', function() {
                const button = document.querySelector('.check-btn');
                button.textContent = 'Checking...';
                button.disabled = true;
            });
            
            // Example URLs for demonstration
            const examples = [
                'https://google.com',
                'https://github.com',
                'https://httpbin.org/status/200',
                'http://example.com',
                'https://jsonplaceholder.typicode.com/posts/1',
                'https://httpstat.us/404'
            ];
            
            input.addEventListener('focus', function() {
                if (!this.value) {
                    this.placeholder = examples[Math.floor(Math.random() * examples.length)];
                }
            });
        });
    </script>
</body>
</html>
